import { Transform, Type } from "class-transformer";
import { <PERSON>Array, IsDate, IsNumber, IsOptional, IsString, ValidateNested, IsIn, IsObject } from "class-validator";
import { Types } from "mongoose";
import { toMongoObjectId } from "src/share/validator";
import { RecurrenceRule } from "../course-event.model";

export class CourseEventDateDto {
  @Type(() => Date)
  @IsDate()
  date: Date;

  @IsString()
  start_date_str: string;

  @IsString()
  end_date_str: string;

  @IsNumber()
  @Transform(({ value }) => Number(value))
  hours: number;
}

export class RecurrenceRuleDto implements RecurrenceRule {
  @IsString()
  @IsIn(['daily', 'weekly', 'monthly', 'yearly'])
  recurrence_type: 'daily' | 'weekly' | 'monthly' | 'yearly';

  @IsNumber()
  @Transform(({ value }) => Number(value))
  recurrence_interval: number;

  @IsOptional()
  @IsString()
  @IsIn(['count', 'date'])
  end_condition?: 'count' | 'date';

  @IsOptional()
  @IsNumber()
  @Transform(({ value }) => Number(value))
  occurrence_count?: number;

  @IsOptional()
  @IsString()
  end_date?: string; // YYYY-MM-DD format

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  week_days?: number[]; // For weekly recurrence [0-6, where 0=Sunday]

  // Time slots for weekly recurrence - each weekday can have different times
  @IsOptional()
  @IsObject()
  week_times_start?: { [weekday: number]: string }; // Start time for each weekday

  @IsOptional()
  @IsObject()
  week_times_end?: { [weekday: number]: string }; // End time for each weekday

  // Single time slot for non-weekly recurrence
  @IsOptional()
  @IsString()
  start_time?: string; // HH:mm format - for daily/monthly/yearly

  @IsOptional()
  @IsString()
  end_time?: string; // HH:mm format - for daily/monthly/yearly

  @IsString()
  first_date: string; // YYYY-MM-DD format
}

export class BulkCreateCourseEventsDto {
  @IsArray()
  @IsArray({ each: true })
  category: string[][];

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  work_content: string;

  @Transform(toMongoObjectId)
  teacher: Types.ObjectId;

  @Transform(toMongoObjectId)
  check_user: Types.ObjectId;

  @IsArray()
  department_ids: number[];

  @IsString()
  @IsOptional()
  note: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CourseEventDateDto)
  course_dates: CourseEventDateDto[];

  @IsOptional()
  @ValidateNested()
  @Type(() => RecurrenceRuleDto)
  recurrence_rule?: RecurrenceRuleDto;
}
