# Hierarchical Category Matching Tests

This document provides test scenarios to verify the new subset matching functionality for course events category filtering.

## Test Data Setup

### Sample Course Events in Database
```javascript
// Course Event 1: Exact match case
{
  "_id": "507f1f77bcf86cd799439011",
  "category": [["数学", "数学Ⅰ"]],
  "name": "数学基础课程",
  "date": "2025-07-17T09:00:00Z"
}

// Course Event 2: Subset match case (contains additional elements)
{
  "_id": "507f1f77bcf86cd799439012", 
  "category": [["数学", "数学Ⅰ", "正课"]],
  "name": "数学正课",
  "date": "2025-07-17T11:00:00Z"
}

// Course Event 3: Another subset match case
{
  "_id": "507f1f77bcf86cd799439013",
  "category": [["数学", "数学Ⅰ", "补课"]],
  "name": "数学补课",
  "date": "2025-07-17T14:00:00Z"
}

// Course Event 4: No match case (different order)
{
  "_id": "507f1f77bcf86cd799439014",
  "category": [["数学Ⅰ", "数学"]],
  "name": "数学进阶课程",
  "date": "2025-07-17T16:00:00Z"
}

// Course Event 5: No match case (different category)
{
  "_id": "507f1f77bcf86cd799439015",
  "category": [["英语", "口语"]],
  "name": "英语口语课程",
  "date": "2025-07-17T18:00:00Z"
}

// Course Event 6: Partial match case (contains first element only)
{
  "_id": "507f1f77bcf86cd799439016",
  "category": [["数学", "数学Ⅱ"]],
  "name": "数学进阶课程",
  "date": "2025-07-17T20:00:00Z"
}
```

## Test Scenarios

### Test 1: Basic Subset Matching

**API Call:**
```
GET /course-events?category_in=[["数学","数学Ⅰ"]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Results:**
- ✅ Course Event 1: `[["数学", "数学Ⅰ"]]` (exact match)
- ✅ Course Event 2: `[["数学", "数学Ⅰ", "正课"]]` (subset match)
- ✅ Course Event 3: `[["数学", "数学Ⅰ", "补课"]]` (subset match)
- ❌ Course Event 4: `[["数学Ⅰ", "数学"]]` (wrong order)
- ❌ Course Event 5: `[["英语", "口语"]]` (different category)
- ❌ Course Event 6: `[["数学", "数学Ⅱ"]]` (partial match but not consecutive)

**Expected Count:** 3 course events

### Test 2: Single Element Matching

**API Call:**
```
GET /course-events?category_in=[["数学"]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Results:**
- ✅ Course Event 1: Contains "数学"
- ✅ Course Event 2: Contains "数学"
- ✅ Course Event 3: Contains "数学"
- ✅ Course Event 4: Contains "数学"
- ❌ Course Event 5: Does not contain "数学"
- ✅ Course Event 6: Contains "数学"

**Expected Count:** 5 course events

### Test 3: No Matches

**API Call:**
```
GET /course-events?category_in=[["物理","力学"]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Results:**
- ❌ All course events (no matches for "物理","力学" sequence)

**Expected Count:** 0 course events

### Test 4: Multiple Filter Arrays (OR Logic)

**API Call:**
```
GET /course-events?category_in=[["数学","数学Ⅰ"],["英语"]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Results:**
- ✅ Course Event 1: Matches ["数学","数学Ⅰ"]
- ✅ Course Event 2: Matches ["数学","数学Ⅰ"]
- ✅ Course Event 3: Matches ["数学","数学Ⅰ"]
- ❌ Course Event 4: No match
- ✅ Course Event 5: Matches ["英语"]
- ❌ Course Event 6: No match

**Expected Count:** 4 course events

## MongoDB Query Verification

### Generated Query for Test 1
```javascript
{
  $or: [
    {
      $expr: {
        $anyElementTrue: {
          $map: {
            input: "$category",
            as: "categoryArray",
            in: {
              $anyElementTrue: {
                $map: {
                  input: { $range: [0, { $max: [0, { $subtract: [{ $add: [{ $size: "$$categoryArray" }, 1] }, 2] }] }] },
                  as: "startIndex",
                  in: {
                    $eq: [
                      ["数学", "数学Ⅰ"],
                      { $slice: ["$$categoryArray", "$$startIndex", 2] }
                    ]
                  }
                }
              }
            }
          }
        }
      }
    }
  ]
}
```

### Generated Query for Test 2 (Single Element)
```javascript
{
  $or: [
    {
      category: {
        $elemMatch: {
          $in: ["数学"]
        }
      }
    }
  ]
}
```

## Backward Compatibility Tests

### Test 5: Exact Array Matching (Legacy Behavior)

**API Call:**
```
GET /course-events?category_in=[["数学","数学Ⅰ","正课"]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Results:**
- ❌ Course Event 1: `[["数学", "数学Ⅰ"]]` (too short)
- ✅ Course Event 2: `[["数学", "数学Ⅰ", "正课"]]` (exact match)
- ❌ Course Event 3: `[["数学", "数学Ⅰ", "补课"]]` (different third element)

**Expected Count:** 1 course event

This test ensures that when a complete specific array is requested, it still works as a subset match.

## Performance Considerations

### Index Recommendations
```javascript
// Recommended index for category field
db.courseevents.createIndex({ "category": 1 })

// Compound index for common queries
db.courseevents.createIndex({ "date": 1, "category": 1 })
```

### Query Optimization Notes
- Single element queries use `$elemMatch` which is more efficient
- Multi-element queries use `$expr` with array operations
- Consider query performance with large datasets
- Monitor query execution times in production

## Error Cases

### Test 6: Invalid Category Format

**API Call:**
```
GET /course-events?category_in=invalid_json&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Behavior:**
- Should handle JSON parsing errors gracefully
- Return appropriate error response
- Log the error for debugging

### Test 7: Empty Category Array

**API Call:**
```
GET /course-events?category_in=[[]]&date_start=2025-07-17&date_end=2025-07-17
```

**Expected Behavior:**
- Should return all course events (no filtering applied)
- Empty filter array should not restrict results
