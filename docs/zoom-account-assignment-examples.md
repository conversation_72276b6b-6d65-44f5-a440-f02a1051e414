# Enhanced Zoom Account Assignment Examples

This document demonstrates how the enhanced `setZoomAccounts` method works with category-based assignment.

## Overview

The enhanced method now implements category-based zoom account assignment with the following features:

1. **Principal Category Extraction**: Extracts the main category from course events
2. **Category-Based Filtering**: Considers zoom accounts with matching categories AND null/undefined categories
3. **Mapping Rule**: Converts "通识语言类" to "通识语言"
4. **Inclusive Strategy**: Includes existing zoom accounts without categories alongside category-specific ones
5. **Preserved Logic**: Maintains all existing time conflict and assignment rules

## Example Scenarios

### Scenario 1: Mathematics Course Events

**Input Course Events:**
```json
[
  {
    "_id": "507f1f77bcf86cd799439011",
    "category": [["数学", "数学Ⅰ", "正课"]],
    "name": "数学基础课程",
    "date": "2024-01-15T09:00:00Z",
    "start_date_str": "09:00",
    "end_date_str": "10:30"
  },
  {
    "_id": "507f1f77bcf86cd799439012", 
    "category": [["数学", "数学Ⅱ", "正课"]],
    "name": "高等数学",
    "date": "2024-01-15T11:00:00Z",
    "start_date_str": "11:00",
    "end_date_str": "12:30"
  }
]
```

**Available Zoom Settings:**
```json
[
  { "account": "<EMAIL>", "category": "数学" },
  { "account": "<EMAIL>", "category": "数学" },
  { "account": "<EMAIL>", "category": "通识语言" },
  { "account": "<EMAIL>", "category": null }
]
```

**Process:**
1. Principal category extracted: "数学"
2. Filtered zoom settings: math.zoom1, math.zoom2, and general.zoom1 (null category)
3. Assignment: Course events prioritize math zoom accounts but can use general accounts if needed

### Scenario 2: Language Course with Mapping Rule

**Input Course Events:**
```json
[
  {
    "_id": "507f1f77bcf86cd799439013",
    "category": [["通识语言类", "英语", "口语"]],
    "name": "英语口语课程",
    "date": "2024-01-16T14:00:00Z",
    "start_date_str": "14:00",
    "end_date_str": "15:30"
  }
]
```

**Process:**
1. Raw principal category: "通识语言类"
2. Mapping rule applied: "通识语言类" → "通识语言"
3. Final principal category: "通识语言"
4. Filtered zoom settings: Language-related zoom accounts + any accounts with null/undefined categories

### Scenario 3: Inclusive Filtering with Unmatched Category

**Input Course Events:**
```json
[
  {
    "_id": "507f1f77bcf86cd799439014",
    "category": [["特殊课程", "实验", "正课"]],
    "name": "特殊实验课程",
    "date": "2024-01-17T10:00:00Z",
    "start_date_str": "10:00",
    "end_date_str": "11:30"
  }
]
```

**Available Zoom Settings:**
```json
[
  { "account": "<EMAIL>", "category": "数学" },
  { "account": "<EMAIL>", "category": "通识语言" },
  { "account": "<EMAIL>", "category": null }
]
```

**Process:**
1. Principal category extracted: "特殊课程"
2. No zoom settings found with exact category "特殊课程"
3. Inclusive filtering includes: general.zoom1 (null category)
4. Assignment proceeds with available null-category zoom accounts

## Implementation Benefits

### 1. **Improved Organization**
- Zoom accounts are now organized by subject/department
- Better resource allocation based on course type
- Reduced conflicts between different course categories

### 2. **Flexible Assignment**
- Automatic category detection from course events
- Smart mapping rules for category variations
- Inclusive filtering ensures maximum zoom account availability

### 3. **Backward Compatibility**
- Existing zoom accounts without categories are included in all assignments
- No assignment failures due to category mismatches
- All existing time conflict logic preserved

### 4. **Enhanced Logging**
- Detailed console output for debugging
- Category extraction process is logged
- Assignment statistics are tracked

## Configuration Examples

### Setting Up Category-Based Zoom Accounts

```bash
# Create zoom settings with categories
POST /zoom-settings
{
  "account": "<EMAIL>",
  "category": "数学"
}

POST /zoom-settings  
{
  "account": "<EMAIL>",
  "category": "通识语言"
}

POST /zoom-settings
{
  "account": "<EMAIL>", 
  "category": "大学院"
}
```

### Bulk Creating Course Events

```bash
POST /course-events/bulk-create
{
  "category": [["数学", "微积分", "正课"]],
  "name": "微积分基础",
  "teacher": "507f1f77bcf86cd799439020",
  "course_dates": [
    {
      "date": "2024-01-20T09:00:00Z",
      "start_date_str": "09:00",
      "end_date_str": "10:30",
      "hours": 1.5
    }
  ]
}
```

The enhanced system will automatically:
1. Extract "数学" as the principal category
2. Filter zoom accounts to include those with category "数学" AND any with null/undefined categories
3. Assign the most available zoom account from the filtered pool
4. Log the entire process for monitoring and debugging

## Key Benefits of Inclusive Filtering

### **Better Resource Utilization**
- Existing zoom accounts without categories remain fully available
- No zoom accounts are unnecessarily excluded from assignment
- Maximum flexibility while maintaining category preferences

### **Seamless Migration**
- Organizations can gradually assign categories to zoom accounts
- No disruption to existing course scheduling
- Smooth transition from non-categorized to categorized system

### **Intelligent Prioritization**
- Category-specific accounts are included alongside general accounts
- Time conflict resolution works across all available accounts
- Best available account is selected regardless of category status
