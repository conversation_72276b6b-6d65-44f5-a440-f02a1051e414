# Inclusive Filtering Test Examples

This document provides test examples to verify the modified `getZoomSettingsByCategory` method works correctly with the new inclusive filtering logic.

## Test Scenarios

### Test 1: Mathematics Course with Mixed Zoom Settings

**Setup:**
```javascript
// Zoom Settings in Database
[
  { account: "<EMAIL>", category: "数学" },
  { account: "<EMAIL>", category: "数学" },
  { account: "<EMAIL>", category: "通识语言" },
  { account: "<EMAIL>", category: null },
  { account: "<EMAIL>", category: undefined },
  { account: "<EMAIL>" } // No category field
]

// Course Events
[
  {
    category: [["数学", "微积分", "正课"]],
    name: "微积分课程"
  }
]
```

**Expected Result:**
- Principal category: "数学"
- Included zoom settings: math1, math2, general1, general2, legacy
- Excluded zoom settings: lang1 (different non-null category)

**MongoDB Query Generated:**
```javascript
{
  $or: [
    { category: "数学" },           // Matching category
    { category: null },            // Null category
    { category: undefined },       // Undefined category
    { category: { $exists: false } } // No category field
  ]
}
```

### Test 2: Language Course with Mapping Rule

**Setup:**
```javascript
// Course Events
[
  {
    category: [["通识语言类", "英语", "口语"]],
    name: "英语口语课程"
  }
]
```

**Expected Process:**
1. Raw principal category: "通识语言类"
2. Mapping applied: "通识语言类" → "通识语言"
3. Final query uses "通识语言" as the category
4. Includes: language accounts + null/undefined category accounts
5. Excludes: math accounts, graduate accounts, etc.

### Test 3: Unknown Category with Null/Undefined Fallback

**Setup:**
```javascript
// Zoom Settings
[
  { account: "<EMAIL>", category: "数学" },
  { account: "<EMAIL>", category: "通识语言" },
  { account: "<EMAIL>", category: null },
  { account: "<EMAIL>" } // No category field
]

// Course Events
[
  {
    category: [["新课程类型", "实验", "正课"]],
    name: "新实验课程"
  }
]
```

**Expected Result:**
- Principal category: "新课程类型"
- No exact category matches found
- Included zoom settings: general1, legacy (null/undefined categories)
- Excluded zoom settings: math1, lang1 (different non-null categories)

### Test 4: No Principal Category (Edge Case)

**Setup:**
```javascript
// Course Events with invalid category
[
  {
    category: [],  // Empty category array
    name: "特殊课程"
  }
]
```

**Expected Result:**
- Principal category: null
- Method returns all zoom settings (fallback behavior)
- No filtering applied

## Verification Steps

### 1. Database Setup
```javascript
// Insert test zoom settings
db.zoomsettings.insertMany([
  { account: "<EMAIL>", category: "数学" },
  { account: "<EMAIL>", category: "数学" },
  { account: "<EMAIL>", category: "通识语言" },
  { account: "<EMAIL>", category: null },
  { account: "<EMAIL>" }
]);
```

### 2. Test Query Execution
```javascript
// Test the MongoDB query directly
db.zoomsettings.find({
  $or: [
    { category: "数学" },
    { category: null },
    { category: undefined },
    { category: { $exists: false } }
  ]
});
```

### 3. Expected Results Validation
- **Should Include**: Accounts with matching category + null/undefined categories
- **Should Exclude**: Accounts with different non-null categories
- **Should Log**: Clear indication of filtering logic and results count

## Benefits Verification

### ✅ **Backward Compatibility**
- Existing zoom accounts without categories are always included
- No course events fail to get zoom account assignments
- Gradual migration to categorized system is supported

### ✅ **Resource Optimization**
- Maximum pool of available zoom accounts for each course type
- Category-specific accounts are preferred but not exclusive
- Better utilization of all available resources

### ✅ **Flexible Configuration**
- Organizations can mix categorized and non-categorized zoom accounts
- No forced migration required for existing setups
- Smooth transition path for implementing categories

## Logging Output Examples

### Successful Category Match
```
Principal category determined: 数学
Found 4 zoom settings for category '数学' (includes matching category and null/undefined categories)
```

### No Category Match (Using Null/Undefined Only)
```
Principal category determined: 新课程类型
Found 2 zoom settings for category '新课程类型' (includes matching category and null/undefined categories)
```

### No Principal Category
```
Principal category determined: null
Found 5 zoom settings for category 'null' (includes matching category and null/undefined categories)
```
