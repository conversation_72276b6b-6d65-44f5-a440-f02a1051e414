# Hierarchical Category Matching Documentation

## Overview

The course events filtering system now supports hierarchical category matching with subset/partial matching capabilities. Instead of requiring exact array matches, the system can find course events where the requested category array is a consecutive subset of any category array in the course event.

## Key Changes

### Before (Exact Matching)
```javascript
// API Call: GET /course-events?category_in=[["数学","数学Ⅰ"]]
// Would only match: [["数学","数学Ⅰ"]]
// Would NOT match: [["数学","数学Ⅰ","正课"]]
```

### After (Subset Matching)
```javascript
// API Call: GET /course-events?category_in=[["数学","数学Ⅰ"]]
// Matches: [["数学","数学Ⅰ"]] (exact)
// Matches: [["数学","数学Ⅰ","正课"]] (subset)
// Matches: [["数学","数学Ⅰ","补课"]] (subset)
// Does NOT match: [["数学Ⅰ","数学"]] (wrong order)
```

## Implementation Details

### MongoDB Query Generation

The system generates different MongoDB queries based on the filter array length:

#### Single Element Queries
```javascript
// For category_in=[["数学"]]
{
  category: {
    $elemMatch: {
      $in: ["数学"]
    }
  }
}
```

#### Multi-Element Queries (Consecutive Subset)
```javascript
// For category_in=[["数学","数学Ⅰ"]]
{
  $expr: {
    $anyElementTrue: {
      $map: {
        input: "$category",
        as: "categoryArray",
        in: {
          $anyElementTrue: {
            $map: {
              input: { $range: [0, { $max: [0, { $subtract: [{ $add: [{ $size: "$$categoryArray" }, 1] }, 2] }] }] },
              as: "startIndex",
              in: {
                $eq: [
                  ["数学", "数学Ⅰ"],
                  { $slice: ["$$categoryArray", "$$startIndex", 2] }
                ]
              }
            }
          }
        }
      }
    }
  }
}
```

## API Usage Examples

### Basic Subset Matching
```bash
GET /course-events?category_in=%5B%5B%22%E6%95%B0%E5%AD%A6%22%2C%22%E6%95%B0%E5%AD%A6%E2%85%A0%22%5D%5D&date_start=2025-07-17&date_end=2025-07-17
```
URL Decoded: `category_in=[["数学","数学Ⅰ"]]`

**Matches:**
- Course events with category `[["数学","数学Ⅰ"]]`
- Course events with category `[["数学","数学Ⅰ","正课"]]`
- Course events with category `[["数学","数学Ⅰ","补课"]]`

### Single Element Matching
```bash
GET /course-events?category_in=%5B%5B%22%E6%95%B0%E5%AD%A6%22%5D%5D&date_start=2025-07-17&date_end=2025-07-17
```
URL Decoded: `category_in=[["数学"]]`

**Matches:**
- Any course event containing "数学" in any category array

### Multiple Filter Arrays (OR Logic)
```bash
GET /course-events?category_in=%5B%5B%22%E6%95%B0%E5%AD%A6%22%2C%22%E6%95%B0%E5%AD%A6%E2%85%A0%22%5D%2C%5B%22%E8%8B%B1%E8%AF%AD%22%5D%5D
```
URL Decoded: `category_in=[["数学","数学Ⅰ"],["英语"]]`

**Matches:**
- Course events matching the "数学","数学Ⅰ" sequence OR containing "英语"

## Matching Rules

### ✅ What Matches

1. **Exact Match**: Filter array exactly equals a category array
   - Filter: `["数学","数学Ⅰ"]`
   - Matches: `[["数学","数学Ⅰ"]]`

2. **Subset Match**: Filter array is a consecutive subset
   - Filter: `["数学","数学Ⅰ"]`
   - Matches: `[["数学","数学Ⅰ","正课"]]`

3. **Single Element**: Filter element exists anywhere
   - Filter: `["数学"]`
   - Matches: `[["数学","数学Ⅰ"]]`, `[["其他","数学"]]`

### ❌ What Doesn't Match

1. **Wrong Order**: Elements in different order
   - Filter: `["数学","数学Ⅰ"]`
   - Does NOT match: `[["数学Ⅰ","数学"]]`

2. **Non-Consecutive**: Elements exist but not consecutively
   - Filter: `["数学","正课"]`
   - Does NOT match: `[["数学","数学Ⅰ","正课"]]`

3. **Missing Elements**: Filter contains elements not in category
   - Filter: `["数学","物理"]`
   - Does NOT match: `[["数学","数学Ⅰ"]]`

## Backward Compatibility

The new system maintains backward compatibility:

- **Exact matches still work**: If you request a specific complete array, it will match exactly
- **API interface unchanged**: Same `category_in` parameter format
- **DtoPipe processing**: No changes to parameter transformation logic

## Performance Considerations

### Query Optimization
- Single element queries use efficient `$elemMatch` operator
- Multi-element queries use `$expr` with array operations
- Consider adding indexes for frequently queried categories

### Recommended Indexes
```javascript
// Basic category index
db.courseevents.createIndex({ "category": 1 })

// Compound index for date + category queries
db.courseevents.createIndex({ "date": 1, "category": 1 })
```

### Performance Tips
- Single element filters are more efficient than multi-element
- Limit date ranges to improve query performance
- Monitor query execution times in production

## Error Handling

The system handles various error cases gracefully:

- **Invalid JSON**: Returns appropriate error response
- **Empty arrays**: No filtering applied (returns all results)
- **Malformed category_in**: Logs error and continues without category filtering

## Migration Notes

### For Existing Applications
- No code changes required for existing API calls
- Existing exact matches will continue to work
- New subset matching is automatically available

### For Database
- No schema changes required
- Existing category data works with new matching logic
- Consider adding performance indexes if needed

## Examples in Different Languages

### JavaScript/TypeScript
```typescript
// Fetch math courses that include "数学" and "数学Ⅰ" sequence
const response = await fetch('/course-events?' + new URLSearchParams({
  category_in: JSON.stringify([["数学", "数学Ⅰ"]]),
  date_start: '2025-07-17',
  date_end: '2025-07-17'
}));
```

### Python
```python
import requests
import json

# Fetch courses with subset matching
params = {
    'category_in': json.dumps([["数学", "数学Ⅰ"]]),
    'date_start': '2025-07-17',
    'date_end': '2025-07-17'
}
response = requests.get('/course-events', params=params)
```

### cURL
```bash
curl -G "/course-events" \
  --data-urlencode 'category_in=[["数学","数学Ⅰ"]]' \
  --data-urlencode 'date_start=2025-07-17' \
  --data-urlencode 'date_end=2025-07-17'
```
