/**
 * MongoDB Migration Script: Add category field to ZoomSetting documents
 * 
 * This script adds the optional category field to existing zoom setting documents.
 * The category field will be set to null for existing documents, allowing users
 * to update them later through the API.
 * 
 * Usage:
 * 1. Via MongoDB shell: mongo your_database_name scripts/migrate-zoom-settings-category.js
 * 2. Via MongoDB Compass: Copy the migration logic below
 */

print("开始迁移 Zoom 设置分类字段...");

// Get the database (assumes you're already connected to the correct database)
const db = db.getSiblingDB(db.getName());

// Find all zoom settings that don't have the category field
const zoomSettings = db.zoomsettings.find({ category: { $exists: false } });

let migratedCount = 0;
let totalFound = 0;

zoomSettings.forEach(function(doc) {
    totalFound++;
    
    try {
        // Add the category field as null (optional field)
        const result = db.zoomsettings.updateOne(
            { _id: doc._id },
            { $set: { category: null } }
        );
        
        if (result.modifiedCount > 0) {
            print("迁移 Zoom 设置: " + doc._id + " | account: " + doc.account + " | category: null");
            migratedCount++;
        }
    } catch (error) {
        print("错误迁移 Zoom 设置 " + doc._id + ": " + error.message);
    }
});

print("迁移完成!");
print("总计找到: " + totalFound);
print("成功迁移: " + migratedCount);
print("跳过: " + (totalFound - migratedCount));

// Verify the migration
const totalWithCategory = db.zoomsettings.find({ category: { $exists: true } }).count();
const totalWithoutCategory = db.zoomsettings.find({ category: { $exists: false } }).count();

print("\n验证结果:");
print("有 category 字段的文档: " + totalWithCategory);
print("没有 category 字段的文档: " + totalWithoutCategory);

if (totalWithoutCategory === 0) {
    print("✅ 迁移成功！所有 Zoom 设置都已包含 category 字段");
} else {
    print("⚠️  还有 " + totalWithoutCategory + " 个文档没有 category 字段");
}
