/**
 * TypeScript Migration Script for ZoomSetting category field
 * 
 * This script provides both forward and rollback migration capabilities
 * for adding the category field to zoom setting documents.
 */

import { MongoClient, Db, Collection } from 'mongodb';

interface ZoomSetting {
  _id: any;
  account: string;
  category?: string | null;
  [key: string]: any;
}

interface MigrationResult {
  message: string;
  totalFound: number;
  migrated: number;
  skipped: number;
  errors: number;
  details?: string[];
}

class ZoomSettingsCategoryMigration {
  private db: Db;
  private collection: Collection<ZoomSetting>;

  constructor(db: Db) {
    this.db = db;
    this.collection = db.collection('zoomsettings'); // Adjust collection name if needed
  }

  /**
   * Validate current migration state
   */
  async validateMigration(): Promise<any> {
    const totalDocuments = await this.collection.countDocuments();
    const withCategory = await this.collection.countDocuments({ category: { $exists: true } });
    const withoutCategory = await this.collection.countDocuments({ category: { $exists: false } });

    const migrationComplete = withoutCategory === 0;

    return {
      totalDocuments,
      withCategory,
      withoutCategory,
      migrationComplete,
      message: migrationComplete 
        ? "所有 Zoom 设置都已包含 category 字段" 
        : `还有 ${withoutCategory} 个文档需要迁移`
    };
  }

  /**
   * Forward migration: Add category field
   */
  async migrateForward(): Promise<MigrationResult> {
    console.log("开始迁移 Zoom 设置分类字段...");
    
    const cursor = this.collection.find({ category: { $exists: false } });
    
    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    let totalFound = 0;
    const details: string[] = [];

    for await (const doc of cursor) {
      totalFound++;
      
      try {
        await this.collection.updateOne(
          { _id: doc._id },
          { $set: { category: null } }
        );
        
        details.push(`迁移成功: ${doc._id} | account: ${doc.account} | category: null`);
        migratedCount++;
      } catch (error) {
        details.push(`错误: ${doc._id} | ${error.message}`);
        errorCount++;
      }
    }

    const result: MigrationResult = {
      message: "Zoom 设置分类字段迁移完成",
      totalFound,
      migrated: migratedCount,
      skipped: skippedCount,
      errors: errorCount,
      details
    };

    console.log("迁移结果:", JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * Rollback migration: Remove category field
   */
  async rollback(): Promise<MigrationResult> {
    console.log("开始回滚 Zoom 设置分类字段...");
    
    const result = await this.collection.updateMany(
      { category: { $exists: true } },
      { $unset: { category: "" } }
    );

    const rollbackResult: MigrationResult = {
      message: "Zoom 设置分类字段回滚完成",
      totalFound: 0,
      migrated: result.modifiedCount,
      skipped: 0,
      errors: 0
    };

    console.log("回滚结果:", JSON.stringify(rollbackResult, null, 2));
    return rollbackResult;
  }
}

/**
 * Usage example:
 * 
 * const client = new MongoClient('mongodb://localhost:27017');
 * await client.connect();
 * const db = client.db('your_database_name');
 * 
 * const migration = new ZoomSettingsCategoryMigration(db);
 * 
 * // Check current state
 * const validation = await migration.validateMigration();
 * console.log(validation);
 * 
 * // Run migration
 * const result = await migration.migrateForward();
 * console.log(result);
 * 
 * // Rollback if needed
 * // const rollbackResult = await migration.rollback();
 * // console.log(rollbackResult);
 * 
 * await client.close();
 */

export { ZoomSettingsCategoryMigration };
