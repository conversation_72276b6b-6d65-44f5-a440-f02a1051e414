# Database Migration Guide

This guide explains how to perform database migrations for the student management system.

## Available Migrations

1. [CourseEvent Category Migration](#courseevent-category-migration) - Migrate category field from `string[]` to `string[][]`
2. [Student Course Expired Date Migration](#student-course-expired-date-migration) - Add `course_expired_date` field to students
3. [ZoomSetting Category Migration](#zoomsetting-category-migration) - Add optional `category` field to zoom settings

---

## CourseEvent Category Migration

This migration transforms the CourseEvent `category` field from `string[]` to `string[][]` format.

## Overview

The migration transforms existing category data to support hierarchical categories:

**Before (string[]):**
```json
{
  "category": ["数学", "数学Ⅰ", "正课"]
}
```

**After (string[][]):**
```json
{
  "category": [["数学", "数学Ⅰ", "正课"]]
}
```

## Migration Methods

### Method 1: API Endpoints (Recommended)

#### 1. Validate Current State
```bash
GET /course-events/validate-migration
```

**Response:**
```json
{
  "totalDocuments": 150,
  "newFormat": 0,
  "oldFormat": 150,
  "emptyCategories": 5,
  "migrationComplete": false,
  "message": "还有 150 个文档需要迁移"
}
```

#### 2. Run Migration
```bash
POST /course-events/migrate-categories
```

**Response:**
```json
{
  "message": "分类数据迁移完成",
  "totalFound": 150,
  "migrated": 145,
  "skipped": 0,
  "errors": 0
}
```

#### 3. Validate Migration
```bash
GET /course-events/validate-migration
```

**Response:**
```json
{
  "totalDocuments": 150,
  "newFormat": 145,
  "oldFormat": 0,
  "emptyCategories": 5,
  "migrationComplete": true,
  "message": "迁移已完成"
}
```

#### 4. Rollback (if needed)
```bash
POST /course-events/rollback-categories
```

### Method 2: MongoDB Shell Script

```bash
mongo your_database_name scripts/migrate-categories.js
```

### Method 3: Direct MongoDB Commands

```javascript
// Connect to your database
use your_database_name

// Run migration
db.courseevents.find({category: {$exists: true, $ne: null}}).forEach(function(doc) {
  if (Array.isArray(doc.category) && doc.category.length > 0) {
    if (!Array.isArray(doc.category[0])) {
      var newCategory = [doc.category]; // Wrap entire array as first element
      db.courseevents.updateOne(
        {_id: doc._id},
        {$set: {category: newCategory}}
      );
      print("Migrated: " + doc._id);
    }
  }
});
```

## Pre-Migration Checklist

1. **Backup Database**
   ```bash
   mongodump --db your_database_name --collection courseevents
   ```

2. **Test on Development Environment**
   - Run migration on a copy of production data
   - Verify application functionality
   - Test all category-related features

3. **Check Application Dependencies**
   - Ensure all code changes are deployed
   - Verify DTOs are updated
   - Test API endpoints

## Post-Migration Verification

### 1. Data Integrity Check
```bash
# Check for any remaining old format
db.courseevents.find({"category.0": {$type: "string"}}).count()
# Should return 0

# Check new format
db.courseevents.find({"category.0": {$type: "array"}}).count()
# Should return total migrated count
```

### 2. Application Testing
- Test course event creation
- Test category filtering
- Test course event updates
- Test salary calculation (uses category data)

### 3. Performance Verification
- Monitor query performance
- Check aggregation pipeline efficiency
- Verify filtering operations

## Rollback Procedure

If issues are discovered after migration:

1. **Stop Application Traffic**
2. **Run Rollback**
   ```bash
   POST /course-events/rollback-categories
   ```
3. **Verify Rollback**
   ```bash
   GET /course-events/validate-migration
   ```
4. **Deploy Previous Code Version**

## Common Issues & Solutions

### Issue: Migration Fails Partially
**Solution:** Re-run migration - it skips already migrated documents

### Issue: Performance Degradation
**Solution:** Add indexes on category fields if needed
```javascript
db.courseevents.createIndex({"category": 1})
```

### Issue: Application Errors After Migration
**Solution:** Verify all DTOs are updated and code is deployed

## Migration Timeline

1. **Preparation:** 30 minutes
   - Database backup
   - Code deployment
   - Testing preparation

2. **Migration:** 5-15 minutes
   - Depends on data volume
   - Monitor progress via logs

3. **Verification:** 15 minutes
   - Data integrity checks
   - Application testing
   - Performance monitoring

## Support

For issues during migration:
1. Check application logs
2. Verify database connection
3. Use validation endpoint to check status
4. Contact development team if needed

---

## ZoomSetting Category Migration

This migration adds an optional `category` field to existing zoom setting documents.

## Overview

The migration adds a new optional field to support categorizing zoom accounts:

**Before:**
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "account": "<EMAIL>"
}
```

**After:**
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "account": "<EMAIL>",
  "category": null
}
```

The `category` field supports the following values:
- "大学院" (Graduate School)
- "学部" (Undergraduate)
- "数学" (Mathematics)
- "通识语言" (General Language)
- "其他" (Others)

## Migration Methods

### Method 1: MongoDB Shell Script (Recommended)

```bash
mongo your_database_name scripts/migrate-zoom-settings-category.js
```

### Method 2: TypeScript Migration

```typescript
import { ZoomSettingsCategoryMigration } from './scripts/zoom-settings-category-migration';
import { MongoClient } from 'mongodb';

const client = new MongoClient('mongodb://localhost:27017');
await client.connect();
const db = client.db('your_database_name');

const migration = new ZoomSettingsCategoryMigration(db);
const result = await migration.migrateForward();
console.log(result);

await client.close();
```

### Method 3: Direct MongoDB Commands

```javascript
// Connect to your database
use your_database_name

// Add category field to documents that don't have it
db.zoomsettings.updateMany(
  { category: { $exists: false } },
  { $set: { category: null } }
);
```

## Pre-Migration Checklist

1. **Backup Database**
   ```bash
   mongodump --db your_database_name --collection zoomsettings
   ```

2. **Test on Development Environment**
   - Run migration on a copy of production data
   - Verify application functionality
   - Test zoom setting CRUD operations

3. **Check Application Dependencies**
   - Ensure all code changes are deployed
   - Verify DTOs are updated
   - Test API endpoints

## Post-Migration Verification

### 1. Data Integrity Check
```bash
# Check documents without category field
db.zoomsettings.find({ category: { $exists: false } }).count()
# Should return 0

# Check documents with category field
db.zoomsettings.find({ category: { $exists: true } }).count()
# Should return total document count
```

### 2. Application Testing
- Test zoom setting creation with category
- Test zoom setting updates
- Test category filtering
- Test zoom account assignment to course events

### 3. Performance Verification
- Monitor query performance
- Consider adding index if category filtering is heavily used:
  ```javascript
  db.zoomsettings.createIndex({"category": 1})
  ```

## Rollback Procedure

If issues are discovered after migration:

1. **Stop Application Traffic**
2. **Run Rollback**
   ```bash
   mongo your_database_name -eval "
   db.zoomsettings.updateMany(
     {category: {\$exists: true}},
     {\$unset: {category: ''}}
   )"
   ```
3. **Verify Rollback**
   ```bash
   db.zoomsettings.find({ category: { $exists: true } }).count()
   # Should return 0
   ```
4. **Deploy Previous Code Version**

## Common Issues & Solutions

### Issue: Migration Fails Partially
**Solution:** Re-run migration - it only affects documents without the category field

### Issue: Validation Errors After Migration
**Solution:** Verify category values are from allowed set: "大学院", "学部", "数学", "通识语言", "其他"

### Issue: Application Errors After Migration
**Solution:** Verify all DTOs are updated and code is deployed

## Example Migration Log

```
开始迁移课程事件分类数据...
迁移课程事件 507f1f77bcf86cd799439011: ["数学","数学Ⅰ","正课"] -> [["数学","数学Ⅰ","正课"]]
迁移课程事件 507f1f77bcf86cd799439012: ["英语","口语"] -> [["英语","口语"]]
跳过已迁移的文档: 507f1f77bcf86cd799439013
迁移结果: {
  "message": "分类数据迁移完成",
  "totalFound": 150,
  "migrated": 145,
  "skipped": 5,
  "errors": 0
}
```

---

## Student Course Expired Date Migration

This migration adds the `course_expired_date` field to existing student documents and calculates initial values based on their courses.

### Overview

The migration adds a new field that automatically tracks the latest expiration date among all courses for each student:

**New Field:**
```json
{
  "_id": "507f1f77bcf86cd799439011",
  "name": "张三",
  "courses": [
    {
      "course_name": "数学",
      "expired_date": "2024-12-31T00:00:00.000Z"
    },
    {
      "course_name": "英语",
      "expired_date": "2025-06-30T00:00:00.000Z"
    }
  ],
  "course_expired_date": "2025-06-30T00:00:00.000Z"
}
```

### Migration Methods

#### Method 1: MongoDB Shell Script (Recommended)

```bash
mongo your_database_name scripts/migrate-course-expired-date.js
```

#### Method 2: TypeScript Migration

```typescript
import { CourseExpiredDateMigration } from './scripts/course-expired-date-migration';
import { MongoClient } from 'mongodb';

async function runMigration() {
  const client = new MongoClient('mongodb://localhost:27017');
  await client.connect();

  const db = client.db('your_database_name');
  const migration = new CourseExpiredDateMigration(db);

  try {
    // Run forward migration
    const result = await migration.migrateForward();
    console.log('Migration result:', result);

    // Validate migration
    const validation = await migration.validate();
    console.log('Validation result:', validation);
  } finally {
    await client.close();
  }
}

runMigration().catch(console.error);
```

#### Method 3: Direct MongoDB Commands

```javascript
// Connect to your database
use your_database_name

// Run migration
db.students.find({}).forEach(function(doc) {
  if (!doc.hasOwnProperty('course_expired_date')) {
    var maxExpiredDate = null;

    if (doc.courses && Array.isArray(doc.courses) && doc.courses.length > 0) {
      var validDates = [];

      doc.courses.forEach(function(course) {
        if (course.expired_date && course.expired_date instanceof Date) {
          validDates.push(course.expired_date);
        }
      });

      if (validDates.length > 0) {
        maxExpiredDate = new Date(Math.max.apply(null, validDates.map(function(date) {
          return date.getTime();
        })));
      }
    }

    db.students.updateOne(
      {_id: doc._id},
      {$set: {course_expired_date: maxExpiredDate}}
    );

    print("Migrated: " + doc._id + " | course_expired_date: " + (maxExpiredDate ? maxExpiredDate.toISOString() : "null"));
  }
});
```

### Pre-Migration Checklist

1. **Backup Database**
   ```bash
   mongodump --db your_database_name --collection students
   ```

2. **Test on Development Environment**
   - Run migration on a copy of production data
   - Verify application functionality
   - Test course-related operations

3. **Check Application Dependencies**
   - Ensure all code changes are deployed
   - Verify DTOs are updated
   - Test student creation/update endpoints

### Post-Migration Verification

#### 1. Data Integrity Check
```javascript
// Check total students with the new field
db.students.find({course_expired_date: {$exists: true}}).count()

// Check students with null course_expired_date (no courses or no valid dates)
db.students.find({course_expired_date: null}).count()

// Check students with valid course_expired_date
db.students.find({course_expired_date: {$exists: true, $ne: null}}).count()

// Sample verification - check if course_expired_date matches max course expired_date
db.students.findOne({courses: {$exists: true, $ne: []}})
```

#### 2. Application Testing
- Test student creation with courses
- Test adding courses to existing students
- Test updating course expired_date
- Test removing courses from students
- Verify automatic course_expired_date calculation

#### 3. Performance Verification
- Monitor query performance on course_expired_date field
- Consider adding index if needed:
  ```javascript
  db.students.createIndex({"course_expired_date": 1})
  ```

### Rollback Procedure

If issues are discovered after migration:

#### Method 1: MongoDB Shell Script
```bash
mongo your_database_name -eval "
db.students.updateMany(
  {course_expired_date: {\$exists: true}},
  {\$unset: {course_expired_date: ''}}
)
"
```

#### Method 2: TypeScript Rollback
```typescript
const migration = new CourseExpiredDateMigration(db);
await migration.rollback();
```

### Automatic Field Updates

After migration, the `course_expired_date` field will be automatically maintained by the application:

- **When adding a course**: Field recalculated and updated
- **When updating a course's expired_date**: Field recalculated and updated
- **When removing a course**: Field recalculated and updated
- **When creating a new student with courses**: Field calculated during creation

### Edge Cases Handled

1. **Students with no courses**: `course_expired_date` set to `null`
2. **Courses with null/undefined expired_date**: Ignored in calculation
3. **Courses with invalid dates**: Filtered out during calculation
4. **All courses have null expired_date**: `course_expired_date` set to `null`

### Migration Timeline

1. **Preparation:** 15 minutes
   - Database backup
   - Code deployment verification
   - Testing preparation

2. **Migration:** 2-10 minutes
   - Depends on number of students
   - Monitor progress via logs

3. **Verification:** 10 minutes
   - Data integrity checks
   - Application testing
   - Performance monitoring

### Example Migration Log

```
开始迁移学生课程到期日期字段...
迁移成功: 507f1f77bcf86cd799439011 | course_expired_date: 2025-06-30T00:00:00.000Z | 课程数量: 3
迁移成功: 507f1f77bcf86cd799439012 | course_expired_date: null | 课程数量: 0
迁移成功: 507f1f77bcf86cd799439013 | course_expired_date: 2024-12-31T00:00:00.000Z | 课程数量: 2
跳过已有course_expired_date字段的文档: 507f1f77bcf86cd799439014
迁移结果: {
  "message": "课程到期日期字段迁移完成",
  "totalFound": 250,
  "migrated": 245,
  "skipped": 5,
  "errors": 0
}
```
